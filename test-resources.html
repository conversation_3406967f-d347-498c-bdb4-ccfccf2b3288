<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>资源测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background: #f0f0f0;
        }
        .test-section {
            margin: 20px 0;
            padding: 15px;
            background: white;
            border-radius: 5px;
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
        }
        .status {
            padding: 5px 10px;
            border-radius: 3px;
            margin: 5px 0;
        }
        .success { background: #d4edda; color: #155724; }
        .error { background: #f8d7da; color: #721c24; }
        .loading { background: #fff3cd; color: #856404; }
        img {
            max-width: 200px;
            max-height: 200px;
            margin: 10px;
            border: 1px solid #ddd;
        }
    </style>
</head>
<body>
    <h1>3D跑酷游戏资源测试</h1>
    
    <div class="test-section">
        <h2>纹理文件测试</h2>
        <div id="texture-status"></div>
        <div id="texture-images"></div>
    </div>
    
    <div class="test-section">
        <h2>3D模型文件测试</h2>
        <div id="model-status"></div>
    </div>
    
    <div class="test-section">
        <h2>Three.js 和 OBJLoader 测试</h2>
        <div id="threejs-status"></div>
    </div>

    <!-- 引入Three.js -->
    <script src="https://ztimg.hefei.cc/static/common/js/libs/three.min.js"></script>
    <!-- 引入OBJ加载器 -->
    <script src="https://threejs.org/examples/js/loaders/OBJLoader.js"></script>
    
    <script>
        // 测试纹理文件
        const textures = [
            'data/texture_diffuse.png',
            'data/texture_normal.png',
            'data/texture_roughness.png',
            'data/texture_metallic.png',
            'data/texture_pbr.png',
            'data/shaded.png'
        ];
        
        function addStatus(containerId, message, type) {
            const container = document.getElementById(containerId);
            const div = document.createElement('div');
            div.className = `status ${type}`;
            div.textContent = message;
            container.appendChild(div);
        }
        
        function testTextures() {
            const imageContainer = document.getElementById('texture-images');
            
            textures.forEach(texturePath => {
                const img = new Image();
                img.onload = function() {
                    addStatus('texture-status', `✓ ${texturePath} 加载成功 (${this.width}x${this.height})`, 'success');
                    
                    // 显示图片
                    const displayImg = document.createElement('img');
                    displayImg.src = texturePath;
                    displayImg.title = texturePath;
                    imageContainer.appendChild(displayImg);
                };
                img.onerror = function() {
                    addStatus('texture-status', `✗ ${texturePath} 加载失败`, 'error');
                };
                img.src = texturePath;
            });
        }
        
        function testModel() {
            fetch('data/base.obj')
                .then(response => {
                    if (response.ok) {
                        return response.text();
                    }
                    throw new Error(`HTTP ${response.status}`);
                })
                .then(data => {
                    const lines = data.split('\n').length;
                    const size = (data.length / 1024).toFixed(2);
                    addStatus('model-status', `✓ base.obj 加载成功 (${lines} 行, ${size} KB)`, 'success');
                })
                .catch(error => {
                    addStatus('model-status', `✗ base.obj 加载失败: ${error.message}`, 'error');
                });
        }
        
        function testThreeJS() {
            // 测试Three.js
            if (typeof THREE !== 'undefined') {
                addStatus('threejs-status', `✓ Three.js 加载成功 (版本: ${THREE.REVISION})`, 'success');
                
                // 测试OBJLoader
                if (typeof THREE.OBJLoader !== 'undefined') {
                    addStatus('threejs-status', '✓ OBJLoader 可用', 'success');
                    
                    // 尝试创建OBJLoader实例
                    try {
                        const loader = new THREE.OBJLoader();
                        addStatus('threejs-status', '✓ OBJLoader 实例创建成功', 'success');
                    } catch (error) {
                        addStatus('threejs-status', `✗ OBJLoader 实例创建失败: ${error.message}`, 'error');
                    }
                } else {
                    addStatus('threejs-status', '✗ OBJLoader 不可用', 'error');
                }
            } else {
                addStatus('threejs-status', '✗ Three.js 未加载', 'error');
            }
        }
        
        // 运行测试
        window.addEventListener('load', function() {
            addStatus('texture-status', '开始测试纹理文件...', 'loading');
            addStatus('model-status', '开始测试3D模型文件...', 'loading');
            addStatus('threejs-status', '开始测试Three.js库...', 'loading');
            
            testTextures();
            testModel();
            testThreeJS();
        });
    </script>
</body>
</html>
